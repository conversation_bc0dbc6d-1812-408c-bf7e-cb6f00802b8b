import { EditOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import PartialFilterModal from './PartialFilterModal';

interface FilterItemProps {
  id?: string;
  componentName: string;
  props: any;
}

const getSetting = function (node: any): FilterItemProps[] {
  const schema = node.schema;
  const setting: any = [];
  const rowSchema = schema.children?.[0] || {};
  rowSchema.children?.forEach((v) => {
    const child = v.children?.[0];
    if (child) {
      setting.push({
        id: child.id,
        componentName: child.componentName,
        props: child.props,
      });
    }
  });
  return setting;
};

const setSetting = function (node: any, items: FilterItemProps[]) {
  const uuid = node.getPropValue('uuid');
  const reportId = node.getPropValue('reportId');
  const schema = node.schema;
  schema.children = [
    {
      componentName: 'FDRow',
      props: {
        uuid,
        reportId,
      },
      children: items
        .map((item) => {
          return {
            componentName: 'FDCell',
            props: {
              uuid,
              reportId,
            },
            children: [
              {
                id: item.id,
                componentName: item.componentName,
                props: {
                  uuid,
                  reportId,
                  partialContainerFilterId: node.id,
                  ...item.props,
                },
              },
            ],
          };
        })
        .concat([
          {
            componentName: 'FDCell',
            props: {
              uuid,
              reportId,
              // @ts-expect-error
              isDefaultFilter: true,
            },
            children: [
              {
                id: undefined,
                componentName: 'SearchButton',
                props: {
                  uuid,
                  reportId,
                  partialContainerFilterId: node.id,
                  isDefaultFilter: true,
                },
              },
            ],
          },
        ]),
    },
  ];

  node.replaceWith(schema);
};

export default function FilterEditor(props) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { config } = window.AliLowCodeEngine;
  // 报告id
  const reportId = config.get('reportId');
  // 当前组件节点
  const node = props.node?._children?.children?.[0];

  if (!node || node.componentName !== 'PartialContainerFilter') return null;

  // 组件配置项
  const componentProps = node.schema.props || {};
  // 筛选器列表, 已有的条件需要回填
  const filterList = getSetting(node);
  console.log('componentProps', node);
  console.log('filterList', filterList);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };
  // 局部筛选器保存函数
  const onSave = (conditions: any) => {
    console.log('conditions', conditions);
    // 保存，走联动规则
    // linkageCenter.notify('save');
    // queryCenter.resetTableQuery();
    // relationCenter.notify('all');
  };
  return (
    <div className="lc-borders-action">
      <EditOutlined
        onClick={() => {
          handleOpenModal();
          // const newFilters = [
          //   ...filterList,
          //   {
          //     componentName: 'InputFilter',
          //     props: {
          //       title: '新建筛选器',
          //       chartLinks: [],
          //     },
          //   },
          // ];
          // setSetting(node, newFilters);
        }}
      />
      <PartialFilterModal
        isModalOpen={isModalOpen}
        handleCloseModal={handleCloseModal}
        onSave={onSave}
      ></PartialFilterModal>
    </div>
  );
}
