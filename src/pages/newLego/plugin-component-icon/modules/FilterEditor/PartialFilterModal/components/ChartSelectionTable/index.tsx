import { useState, useCallback, useMemo, useEffect } from 'react';
import { Table, Checkbox, Select, Button, Tooltip } from '@blmcp/ui';
import type { ColumnsType } from '@blmcp/ui';
import { FilterCondition } from '../../index';

import './index.less';

interface ChartData {
  id: string;
  name: string;
  type: string;
  dataSource: string;
}

interface FieldOption {
  value: string;
  label: string;
}

interface ChartSelectionTableProps {
  conditionType: 'time' | 'range' | 'text' | 'list' | null;
  selectedCharts: string[];
  selectedFields: Record<string, string>;
  onSelectionChange: (
    selectedCharts: string[],
    selectedFields: Record<string, string>,
  ) => void;
}

// Mock数据
const mockChartData: ChartData[] = [
  { id: '1', name: '销售趋势图', type: '折线图', dataSource: '销售数据' },
  { id: '2', name: '用户分布饼图', type: '饼图', dataSource: '用户数据' },
  { id: '3', name: '收入柱状图', type: '柱状图', dataSource: '财务数据' },
  { id: '4', name: '地区销量表', type: '表格', dataSource: '销售数据' },
  { id: '5', name: '产品分析图', type: '散点图', dataSource: '产品数据' },
  { id: '11', name: '销售趋势图', type: '折线图', dataSource: '销售数据' },
  { id: '21', name: '用户分布饼图', type: '饼图', dataSource: '用户数据' },
  { id: '31', name: '收入柱状图', type: '柱状图', dataSource: '财务数据' },
  { id: '41', name: '地区销量表', type: '表格', dataSource: '销售数据' },
  { id: '51', name: '产品分析图', type: '散点图', dataSource: '产品数据' },
];

const mockFieldOptions: Record<string, FieldOption[]> = {
  '1': [
    { value: 'date', label: '日期' },
    { value: 'sales', label: '销售额' },
    { value: 'region', label: '地区' },
  ],
  '2': [
    { value: 'user_type', label: '用户类型' },
    { value: 'count', label: '用户数量' },
    { value: 'age_group', label: '年龄段' },
  ],
  '3': [
    { value: 'month', label: '月份' },
    { value: 'revenue', label: '收入' },
    { value: 'department', label: '部门' },
  ],
  '4': [
    { value: 'region', label: '地区' },
    { value: 'product', label: '产品' },
    { value: 'quantity', label: '数量' },
  ],
  '5': [
    { value: 'category', label: '产品类别' },
    { value: 'price', label: '价格' },
    { value: 'rating', label: '评分' },
  ],
  '11': [
    { value: 'date', label: '日期' },
    { value: 'sales', label: '销售额' },
    { value: 'region', label: '地区' },
  ],
  '21': [
    { value: 'user_type', label: '用户类型' },
    { value: 'count', label: '用户数量' },
    { value: 'age_group', label: '年龄段' },
  ],
  '31': [
    { value: 'month', label: '月份' },
    { value: 'revenue', label: '收入' },
    { value: 'department', label: '部门' },
  ],
  '41': [
    { value: 'region', label: '地区' },
    { value: 'product', label: '产品' },
    { value: 'quantity', label: '数量' },
  ],
  '51': [
    { value: 'category', label: '产品类别' },
    { value: 'price', label: '价格' },
    { value: 'rating', label: '评分' },
  ],
};

const ChartSelectionTable = ({
  conditionType,
  selectedCharts,
  selectedFields,
  onSelectionChange,
}: ChartSelectionTableProps) => {
  const [selectAll, setSelectAll] = useState(false);
  // 表格滚动高度
  const [tableHeight, setTableHeight] = useState(300);

  // 处理单个图表选择
  const handleChartSelect = useCallback(
    (chartId: string, checked: boolean) => {
      let newSelectedCharts: string[];
      let newSelectedFields = { ...selectedFields };

      if (checked) {
        newSelectedCharts = [...selectedCharts, chartId];
      } else {
        newSelectedCharts = selectedCharts.filter((id) => id !== chartId);
        // 移除对应的字段选择
        delete newSelectedFields[chartId];
      }

      onSelectionChange(newSelectedCharts, newSelectedFields);
    },
    [selectedCharts, selectedFields, onSelectionChange],
  );

  // 处理全选
  const handleSelectAll = useCallback(() => {
    if (selectAll) {
      // 取消全选
      onSelectionChange([], {});
    } else {
      // 全选
      const allChartIds = mockChartData.map((chart) => chart.id);
      onSelectionChange(allChartIds, selectedFields);
    }
    setSelectAll(!selectAll);
  }, [selectAll, selectedFields, onSelectionChange]);

  // 处理字段选择
  const handleFieldSelect = useCallback(
    (chartId: string, fieldValue: string) => {
      const newSelectedFields = {
        ...selectedFields,
        [chartId]: fieldValue,
      };
      onSelectionChange(selectedCharts, newSelectedFields);
    },
    [selectedCharts, selectedFields, onSelectionChange],
  );
  useEffect(() => {
    const handleResize = () => {
      const modalBody = document.querySelector('.filter-config-panel');
      const modalBodyConfigSection = document.querySelector('.config-section');
      if (modalBody && modalBodyConfigSection) {
        const height =
          modalBody.clientHeight -
          modalBodyConfigSection?.clientHeight -
          24 -
          22 -
          12 -
          12 -
          55; // 留出头部/底部的空间
        setTableHeight(Math.max(height, 300)); // 300 是最小值
      }
    };

    handleResize(); // 初始化计算一次
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 更新全选状态
  useMemo(() => {
    const isAllSelected =
      mockChartData.length > 0 &&
      mockChartData.every((chart) => selectedCharts.includes(chart.id));
    setSelectAll(isAllSelected);
  }, [selectedCharts]);

  const columns: ColumnsType<ChartData> = [
    {
      title: (
        <Tooltip title={!conditionType ? '请先选择筛选器类型' : ''}>
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAll}
            disabled={!conditionType}
          />
        </Tooltip>
      ),
      dataIndex: 'selected',
      key: 'selected',
      width: 48,
      render: (text: string, record: ChartData) => (
        <Tooltip title={!conditionType ? '请先选择筛选器类型' : ''}>
          <Checkbox
            checked={selectedCharts.includes(record.id)}
            onChange={(e) => handleChartSelect(record.id, e.target.checked)}
            disabled={!conditionType}
          />
        </Tooltip>
      ),
    },
    {
      title: '名称',
      dataIndex: 'type',
      key: 'type',
      width: 330,
    },
    {
      title: '数据集',
      dataIndex: 'dataSource',
      key: 'dataSource',
      width: 200,
    },
    {
      title: '配置项',
      key: 'field',
      render: (_, record: ChartData) => {
        const isSelected = selectedCharts.includes(record.id);
        const fieldOptions = mockFieldOptions[record.id] || [];
        return (
          <Select
            placeholder="请选择字段"
            value={selectedFields[record.id]}
            onChange={(value: string) => handleFieldSelect(record.id, value)}
            disabled={!isSelected}
            style={{ width: 120 }}
            options={fieldOptions}
          />
        );
      },
      width: 300,
    },
  ];

  return (
    <div className="chart-selection-table">
      <div className="table-header">
        <div className="selection-actions"></div>
      </div>

      <Table
        columns={columns}
        dataSource={mockChartData}
        rowKey="id"
        pagination={false}
        size="small"
        className="chart-table"
        scroll={{ y: tableHeight }}
      />
    </div>
  );
};

export default ChartSelectionTable;
