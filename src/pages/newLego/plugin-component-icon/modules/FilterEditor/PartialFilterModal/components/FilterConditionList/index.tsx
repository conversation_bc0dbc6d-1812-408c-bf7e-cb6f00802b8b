import { useState, useCallback } from 'react';
import { Button, Input, Tooltip, BLMIconFont } from '@blmcp/ui';
import { useTooltipShow } from '@/pages/lego/libraryMaterials/hooks/useTooltipShow';
import { FilterCondition } from '../../index';
import './index.less';

interface FilterConditionListProps {
  conditions: FilterCondition[];
  activeConditionId: string;
  onAddCondition: () => void;
  onDeleteCondition: (id: string) => void;
  onSelectCondition: (id: string) => void;
  onUpdateConditionName: (id: string, name: string) => void;
}

const FilterConditionList = ({
  conditions,
  activeConditionId,
  onAddCondition,
  onDeleteCondition,
  onSelectCondition,
  onUpdateConditionName,
}: FilterConditionListProps) => {
  // 当前编辑状态的查询条件id
  const [editingId, setEditingId] = useState<string>('');
  // 当前编辑状态的查询条件名称
  const [editingName, setEditingName] = useState<string>('');

  // 开始编辑名称
  const handleStartEdit = useCallback((condition: FilterCondition) => {
    setEditingId(condition.id);
    setEditingName(condition.name);
  }, []);

  // 完成编辑
  const handleFinishEdit = useCallback(() => {
    if (editingId && editingName.trim()) {
      onUpdateConditionName(editingId, editingName.trim());
    }
    setEditingId('');
    setEditingName('');
  }, [editingId, editingName, onUpdateConditionName]);

  // 输入验证
  const validateInput = useCallback((value: string) => {
    return value.length <= 48;
  }, []);

  // 处理输入变化
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (validateInput(value)) {
        setEditingName(value);
      }
    },
    [validateInput],
  );

  // 处理键盘事件
  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleFinishEdit();
      }
    },
    [handleFinishEdit],
  );
  // 创建一个组件来处理单个条件名称的tooltip显示
  const ConditionNameWithTooltip = ({
    condition, // onClick,
  }: {
    condition: FilterCondition;
    // onClick: (e: React.MouseEvent) => void;
  }) => {
    const { tooltipEnable, textRef } = useTooltipShow(condition.name);

    const nameElement = (
      <span ref={textRef} className="condition-name">
        {condition.name}
      </span>
    );

    return tooltipEnable ? (
      <Tooltip title={condition.name}>{nameElement}</Tooltip>
    ) : (
      nameElement
    );
  };
  return (
    <div className="filter-condition-list">
      <div className="condition-header">
        <span className="header-title">查询条件</span>
        <Tooltip title={'局部筛选器最多支持5个'}>
          <BLMIconFont type="BLM-ic-information-o" />
        </Tooltip>
      </div>

      <div className="condition-list">
        {conditions.map((condition) => (
          <div
            key={condition.id}
            className={`condition-item ${
              activeConditionId === condition.id ? 'active' : ''
            } ${condition.isValid === false ? 'error' : ''}`}
            onClick={() => onSelectCondition(condition.id)}
          >
            <div className="condition-content">
              {editingId === condition.id ? (
                <Input
                  value={editingName}
                  onChange={handleInputChange}
                  onBlur={handleFinishEdit}
                  onKeyDown={handleKeyPress}
                  autoFocus
                  className="condition-input"
                  maxLength={48}
                />
              ) : (
                <div className="condition-name-wrapper">
                  {condition.isValid === false && (
                    <Tooltip
                      title={
                        <div style={{ whiteSpace: 'pre-line' }}>
                          {condition.errorMessage?.map((msg, i) => (
                            <div key={i}>{msg}</div>
                          ))}
                        </div>
                      }
                    >
                      <BLMIconFont
                        className="error-icon"
                        type="BLM-ic-caution"
                      />
                    </Tooltip>
                  )}
                  <ConditionNameWithTooltip
                    condition={condition}
                    // onClick={() => onSelectCondition(condition.id)}
                  />
                  <Button
                    type="text"
                    icon={<BLMIconFont type="BLM-ic-edit-o" />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStartEdit(condition);
                    }}
                    className="edit-btn"
                    size="small"
                  />
                  <Button
                    type="text"
                    icon={<BLMIconFont type="BLM-ic-delete-o" />}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      onDeleteCondition(condition.id);
                    }}
                    className="edit-btn"
                    size="small"
                  />
                </div>
              )}
            </div>
          </div>
        ))}

        {conditions.length === 0 && (
          <div className="empty-state">
            <div className="empty-state-png"></div>
            <div className="empty-state-text">暂无数据</div>
          </div>
        )}
      </div>
      <Tooltip title={conditions.length >= 5 ? '局部筛选器最多支持5个' : ''}>
        <Button onClick={onAddCondition} disabled={conditions.length >= 5}>
          新增条件
        </Button>
      </Tooltip>
    </div>
  );
};

export default FilterConditionList;
