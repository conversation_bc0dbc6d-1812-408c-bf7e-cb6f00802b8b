import { setVariable } from '@/utils/common';
import { store } from '@/pages/lego/hooks/useComponent';

const expCenter = setVariable('__lego__bi_QueryCenter', {});
export interface QueryData {
  columnId?: number;
  key: string;
  dataType: number;
  fieldValue?: (string | number)[];
  fieldLabel?: (string | number)[];
  dateFilterRelativeUnit?: number;
  isHidden?: 0 | 1;
  alias?: string | null;
}
export interface QueryCenter {
  query: Record<string, QueryData>;
  tableQuery: Record<string, QueryData>;
  getQuery: (dataType?: number) => any;
  setQuery: (id: string, filterData: QueryData) => void;
  deleteQuery: (columnId: string) => void;
  getFilterQuery: (key: string) => null | (string | number)[];
  clear: () => void;
  setTableQuery: (id: string, data: any) => void;
  getTableQuery: (id: string) => void;
  resetTableQuery: () => void;
}

function getQueryCenter() {
  const queryCenter: QueryCenter = {
    query: {},
    // 存储table 相关查询参数，供导出数据使用。
    tableQuery: {},
    // 本期没有参数，是因为，都是全局查询，后续扩展根据配置收集自己的参数
    getQuery(dataType) {
      const filterInfo: QueryData[] = [];
      // eslint-disable-next-line guard-for-in
      for (let k in this.query) {
        filterInfo.push({
          ...(this.query[k] as QueryData),
          isHidden: store.get(k)?.hidden ? 1 : 0,
          alias: store.get(k)?.title || null,
        });
      }

      filterInfo.sort((a, b) => a.key?.localeCompare(b.key));

      // 新组织逻辑需要带上城运车私参
      const adcodeList =
        filterInfo.find((f) => f.key === 'adcode')?.fieldValue || [];
      const transportCompanyIdList =
        filterInfo.find((f) => f.key === 'car_team_id')?.fieldValue || [];
      const fleetIdList =
        filterInfo.find((f) => f.key === 'fleet_id')?.fieldValue || [];

      // 如果是表格，则传递分页信息
      const tableDataType = [41, 5];
      if (tableDataType.includes(dataType as number)) {
        const paging = {
          // 分页配置
          pageSize: 20, // 分页阈值
          pageNum: 1, // 当前页数
        };
        return {
          filterInfo,
          paging,
          adcodeList,
          transportCompanyIdList,
          fleetIdList,
        };
      }
      // 如果是排行榜，传递分页信息
      else if (dataType === 42) {
        const paging = {
          // 分页配置
          pageSize: 50, // 分页阈值
          pageNum: 1, // 当前页数
        };
        return {
          filterInfo,
          paging,
          adcodeList,
          transportCompanyIdList,
          fleetIdList,
        };
      }
      return { filterInfo, adcodeList, transportCompanyIdList, fleetIdList };
    },
    setQuery(id = '', filterData) {
      if (store.get(id)?.noRender) {
        return;
      }
      this.query[id] = filterData;
    },
    // 编辑态时，删除筛选器，需要清除筛选条件
    deleteQuery(id) {
      if (this.query[id]) {
        delete this.query[id];
      }
    },
    // 获取筛选器值
    getFilterQuery(key: string) {
      const filterInfos = Object.values(this.query);
      return (
        filterInfos.find((item: QueryData) => item.key === key)?.fieldValue ||
        []
      );
    },
    clear() {
      this.query = {};
    },
    // set table query
    setTableQuery(id: string, data: any) {
      this.tableQuery[id] = data;
    },
    getTableQuery(id: string) {
      return this.tableQuery[id];
    },
    resetTableQuery() {
      return (this.tableQuery = {});
    },
  };
  return queryCenter;
}

function exportFunction(reportId: string) {
  if (!reportId) {
    console.error('reportId is required');
  }
  return expCenter[reportId] || (expCenter[reportId] = getQueryCenter());
}
const funExp = getQueryCenter();
// eslint-disable-next-line guard-for-in
for (let k in funExp) {
  (expCenter as any)[k] = (funExp as any)[k];
  (exportFunction as any)[k] = (funExp as any)[k];
}

export default exportFunction;
