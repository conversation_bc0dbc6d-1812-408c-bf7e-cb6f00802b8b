import { useEffect, useMemo, useCallback, CSSProperties, useRef } from 'react';
import { Empty } from '@blmcp/ui';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';
import { debounce } from 'lodash-es';
import useComponentData from '@/pages/lego/hooks/useComponentData';
import { ComponentProps } from '@/pages/lego/type';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';

import { globalCache } from '@/pages/lego/utils/cache';
import isMobile from '@/pages/lego/utils/isMobile';
import ComponentTitle from '../ComponentTitle';
import ChartContainer from '../ChartContainer';
import { isValueValid } from '../../../utils/is';
import emptyIcon from './image.png';
// import { isEqual } from 'lodash-es';

interface queryOption {
  // 是否使用缓存， 默认 true
  cache?: boolean;
  // 本次请求是否复用
  repeatedSubmission?: boolean;
}
interface TitleConfig {
  show: boolean;
  border: boolean;
}

interface Option {
  // 不获取数据
  notQueryData?: boolean;
  // 自定义数据查询拦截
  queryCallback?: (
    props: ComponentProps<any>,
    meta: unknown,
  ) => boolean | unknown;
  // 数据处理
  handleData?: (data: any, props?: ComponentProps<any>) => any;
  // props 处理
  handleProps?: (props: ComponentProps<any>, data: any) => any;
  // 不设置默认样式
  notDefaultStyle?: boolean;
  // 默认高
  defaultHeight?: string;
  containerStyle?: object;
  wrapperStyle?: CSSProperties;
  wrapperClass?: string;
  // 标题配置
  titleConfig?: boolean | TitleConfig;
  showTitleArrow?: boolean;
}

export default (Component: JSX.ElementType, option: Option = {}) =>
  (props: ComponentProps<any>) => {
    const {
      notQueryData,
      queryCallback,
      titleConfig = { show: true, border: true },
      handleData,
      handleProps,
      notDefaultStyle = false,
      defaultHeight,
      containerStyle = {},
      wrapperStyle = {},
      wrapperClass = '',
      showTitleArrow,
    } = option;
    const domRef = useRef<HTMLDivElement>(null);
    const showTitle = titleConfig === true || titleConfig.show;
    const { dataSetConfig, componentId, __id, uuid, reportId } = props;
    const isEdit = props.__designMode === 'design';
    const chartId = componentId ?? __id;
    const queryCenter = queryCenterExp(uuid);
    const relationCenter = relationCenterExp(uuid);

    const publishStatus = isEdit ? 0 : reportStore.get(uuid)?.publishStatus;
    const [data, query, [meta, setMeta]] = useComponentData<any>({
      componentId: chartId,
      publishStatus,
      isEdit,
      uuid,
      reportId,
    });
    // 是否是编辑态
    // 数据集diff用
    // const oldDataSetConfig = useRef({});
    // 符合查询条件场景
    const compliance = useMemo(() => {
      // 如果没有数据集id 或者 notQueryData 为true
      if (
        !dataSetConfig ||
        notQueryData ||
        dataSetConfig.dataSourceId === undefined
      )
        return false;

      // 数据集不满足
      let notSatisfied = false;
      const limitMap = meta.dataSetConfig || {};

      for (let k in limitMap) {
        if (!limitMap.hasOwnProperty(k)) continue;
        const limit: number[] = limitMap[k]?.limit || [];
        const min = limit[0] === null ? Infinity : limit[0];
        const max = limit[1] === null ? Infinity : limit[1];
        const valueLength = ((dataSetConfig as any)[k] || []).length;
        if (valueLength < min || valueLength > max) {
          notSatisfied = true;
          break;
        }
      }

      if (notSatisfied) return false;

      // 自定义拦截
      if (queryCallback?.(props, meta) === false) return false;

      // 否则
      return true;
    }, [dataSetConfig, meta, notQueryData, props, queryCallback]);

    useEffect(() => {
      // 编辑态初始化满足查询条件则设置为查询态
      if (compliance && isEdit && relationCenter.isCanBeQueried()) {
        setMeta({ isTriggerQuery: true, loading: true }, true);
      } else if (compliance && !isEdit) {
        // 解决初次加载时卡片没有loading问题
        setMeta({ isTriggerQuery: true, loading: true }, true);
      } else if (compliance) {
        setMeta({ loading: true }, true);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      // 符合查询条件场景 && 组件在可视区 && 触发查询
      if (compliance && meta.inViewDom && meta.isTriggerQuery) {
        query(queryCenter.getQuery(meta.dataType), undefined, dataSetConfig);
        setMeta({ isTriggerQuery: false }, true);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [compliance, meta.inViewDom, meta.isTriggerQuery]);

    // 懒加载监测
    useEffect(() => {
      // if (!meta.isTriggerQuery || !compliance) return () => {};
      // 浏览器不兼容 IntersectionObserver 处理, 直接设置为true
      if (!IntersectionObserver) {
        setMeta({ inViewDom: true }, true);
        return () => {};
      }
      // 懒加载监测
      const observerLazyLoad = new IntersectionObserver(
        debounce((entries) => {
          // if (entries?.[0]?.isIntersecting) {
          //   if (domRef.current) {
          //     observerLazyLoad.unobserve(domRef.current);
          //   }
          //   observerLazyLoad.disconnect();
          // }
          setMeta({ inViewDom: !!entries?.[0]?.isIntersecting }, true);
        }, 200),
      );
      observerLazyLoad.observe(domRef.current as Element);
      return () => {
        if (domRef.current) {
          observerLazyLoad.unobserve(domRef.current);
        }
        observerLazyLoad.disconnect();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      const queryData = (option?: queryOption) => {
        const { cache = true, repeatedSubmission = true } = option || {};
        // 表明是修改数据集调用的不要缓存
        if (!cache) {
          globalCache.deleteByIndexId(chartId);
        }
        if (compliance) {
          // repeatedSubmission 是否重复提交检测， 搜索按钮需要检测，但修改指标部分不需要检测
          setMeta(
            { isTriggerQuery: true, repeatedSubmission, loading: true },
            true,
          );
        }
      };

      relationCenter.subscribe('all', chartId, queryData);
      setMeta({ query: queryData }, true);

      return () => {
        relationCenter.unsubscribe('all', chartId);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [query, compliance]);

    // 编辑态初始化判断是否可查询
    // useEffect(() => {
    //   if (compliance && isEdit && meta.isInitQuery) {
    //     // query(queryCenter.getQuery(meta.dataType));
    //     setMeta({ queryState: false }, true);
    //   }
    // }, []);

    // useEffect(() => {
    //   // 请求过、不符合请求条件 就不要再去请求
    //   if (meta.queryState || !compliance) {
    //     return () => {};
    //   }

    //   // 浏览器不兼容 IntersectionObserver 处理
    //   if (!IntersectionObserver) {
    //     // 由于处理cache 需要将 dataSetConfig 传入
    //     query(queryCenter.getQuery(meta.dataType), undefined, dataSetConfig);
    //     setMeta({ queryState: true }, true);
    //     return () => {};
    //   }
    //   // 懒加载监测
    //   const observerLazyLoad = new IntersectionObserver((entries) => {
    //     if (entries?.[0]?.isIntersecting) {
    //       setMeta({ queryState: true }, true);
    //       // 由于处理cache 需要将 dataSetConfig 传入
    //       query(queryCenter.getQuery(meta.dataType), undefined, dataSetConfig);
    //       if (domRef.current) {
    //         observerLazyLoad.unobserve(domRef.current);
    //       }
    //       observerLazyLoad.disconnect();
    //     }
    //   });
    //   observerLazyLoad.observe(domRef.current as Element);
    //   return () => {
    //     if (domRef.current) {
    //       observerLazyLoad.unobserve(domRef.current);
    //     }
    //     observerLazyLoad.disconnect();
    //   };
    // }, [compliance, meta.queryState]);

    // useEffect(() => {
    //   const queryData = (option?: queryOption) => {
    //     const { cache = true, repeatedSubmission = true } = option || {};
    //     // 表明是修改数据集调用的不要缓存
    //     if (!cache) {
    //       globalCache.deleteByIndexId(chartId);
    //     }
    //     if (compliance) {
    //       if (isEdit) {
    //         setMeta({ isInitQuery: true }, true);
    //       }
    //       // queryState 请求过、不符合请求条件 就不要再去请求
    //       // repeatedSubmission 是否重复提交检测， 搜索按钮需要检测，但修改指标部分不需要检测
    //       setMeta({ queryState: false, repeatedSubmission }, true);
    //     }
    //   };

    //   relationCenter.subscribe('all', chartId, queryData);
    //   setMeta({ query: queryData }, true);

    //   return () => {
    //     relationCenter.unsubscribe('all', chartId);
    //   };
    // }, [query, compliance]);

    // resize 事件注册
    const useResize = useCallback((fn: () => void) => {
      const resizeList = meta.resize || [];
      if (!resizeList.includes(fn)) {
        resizeList.push(fn);
        setMeta({ resize: resizeList }, true);
      }
      return function () {
        const findIndex = resizeList.findIndex((v) => v === fn);
        resizeList.splice(findIndex, 1);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 更新组件配置项
    const updateComponentConfig = useCallback(
      (key: string, value: any) => {
        //@ts-ignore
        window?.__setSelectComponentPropsDataById?.(chartId, key, value);
      },
      [chartId],
    );

    const style: any = {
      width: '100%',
      height: showTitle
        ? isMobile()
          ? 'calc(100% - 28px)'
          : 'calc(100% - 36px)'
        : '100%',
    };

    // 检测是否有数据, 可以渲染
    const isRender = useMemo(() => {
      // 组件配置该属性可立即渲染
      if (notQueryData) return true;
      // 数据集不完善不可渲染
      if (!compliance) return false;

      /** 数据完整性检测 */
      if (meta.dataType === 5) {
        // 交叉表，需要单独处理
        return data?.values?.values?.length > 0;
      } else if (meta.dataType === 31 || meta.dataType === 3) {
        // 指标卡
        return (
          (isValueValid(data?.values) && data?.values?.length) ||
          isValueValid(data?.value)
        );
      } else {
        return data?.values?.length > 0;
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data, notQueryData, compliance]);

    // 数据处理， 抽离出来防止每次变更导致重新渲染
    const dataSource = useMemo(() => {
      if (!isRender) return null;
      return (handleData && handleData(data, props)) || data;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data, isRender, dataSetConfig]);

    // 组件props 处理
    const componentProps = useMemo(() => {
      if (!isRender) return null;
      return (handleProps && handleProps(props, data)) || props;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isRender, props, data]);

    // 储存是否渲染状态
    useEffect(() => {
      setMeta({ isRender, uuid, compliance }, true);
    }, [isRender, compliance]);

    return (
      <ChartContainer
        ref={domRef}
        chartId={chartId}
        compliance={compliance}
        useResize={useResize}
        defaultHeight={defaultHeight}
        containerStyle={containerStyle}
        isEdit={isEdit}
      >
        {showTitle && (
          <ComponentTitle
            {...props}
            publishStatus={publishStatus}
            showTitleArrow={showTitleArrow}
            titleConfig={titleConfig}
            // describe={data?.describe}
            isEdit={isEdit}
            query={queryCenter.getQuery(meta.dataType)}
            isRender={isRender}
          ></ComponentTitle>
        )}
        {(isRender && (
          <div
            style={!notDefaultStyle ? style : wrapperStyle}
            className={wrapperClass}
          >
            <Component
              {...componentProps}
              chartId={chartId}
              isEdit={isEdit}
              // 数据处理层，如果有的话，就执行。
              dataSource={dataSource}
              query={query}
              useResize={useResize}
              updateComponentConfig={updateComponentConfig}
              height={props.height}
              width={props.width}
            />
          </div>
        )) || (
          <div
            style={{
              ...style,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-around',
            }}
          >
            <Empty
              className="lego-card-wrap-empty"
              description={
                compliance && meta.error ? meta.error.msg : '暂无数据'
              }
              image={emptyIcon}
            />
          </div>
        )}
      </ChartContainer>
    );
  };
